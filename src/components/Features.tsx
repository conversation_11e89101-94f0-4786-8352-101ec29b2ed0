
import { Wand, <PERSON>, CloudUpload, Eye, ArrowRight } from "lucide-react";

const Features = () => {
  const features = [
    {
      icon: <CloudUpload className="h-10 w-10 text-ghibli-500" />,
      title: "Easy Upload",
      description: "Simply upload any image from your device or provide a URL to get started."
    },
    {
      icon: <Wand className="h-10 w-10 text-ghibli-600" />,
      title: "Magical Transformations",
      description: "Choose from Ghibli-style, action figure, or other artistic styles for your transformation."
    },
    {
      icon: <Camera className="h-10 w-10 text-ghibli-700" />,
      title: "High Quality Results",
      description: "Get stunning, high-resolution results powered by the latest GPT Image AI models."
    },
    {
      icon: <Eye className="h-10 w-10 text-actionhero-500" />,
      title: "Instant Preview",
      description: "See a real-time preview of your transformation before downloading."
    }
  ];

  const examples = [
    {
      before: "https://source.unsplash.com/random/600x800/?person",
      after: "https://source.unsplash.com/random/600x800/?ghibli-style",
      style: "Ghibli Style"
    },
    {
      before: "https://source.unsplash.com/random/600x800/?woman",
      after: "https://source.unsplash.com/random/600x800/?action-figure",
      style: "Action Hero"
    }
  ];

  return (
    <section id="features" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Transform Images with Advanced AI
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Our platform uses cutting-edge AI models to transform your ordinary photos into extraordinary art.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-24">
          {features.map((feature, index) => (
            <div 
              key={index} 
              className="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow border border-gray-100"
            >
              <div className="mb-4">{feature.icon}</div>
              <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </div>
          ))}
        </div>

        <div id="examples" className="pt-10">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              See the Magic in Action
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Check out these example transformations created with our AI technology.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 lg:gap-12">
            {examples.map((example, index) => (
              <div key={index} className="glass-effect rounded-xl overflow-hidden">
                <div className="relative">
                  <div className="absolute top-4 left-4 z-10">
                    <span className="inline-block bg-white/90 backdrop-blur-sm text-sm font-medium px-3 py-1 rounded-full">
                      Original
                    </span>
                  </div>
                  <div className="absolute top-4 right-4 z-10">
                    <span className="inline-block bg-white/90 backdrop-blur-sm text-sm font-medium px-3 py-1 rounded-full">
                      {example.style}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-2">
                    <div className="aspect-square">
                      <img 
                        src={example.before} 
                        alt="Before transformation" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="aspect-square">
                      <img 
                        src={example.after} 
                        alt="After transformation" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                  
                  {/* Divider arrow */}
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white h-10 w-10 rounded-full shadow-lg flex items-center justify-center">
                    <ArrowRight className="h-5 w-5 text-ghibli-600" />
                  </div>
                </div>
                
                <div className="p-4 bg-white/80">
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="font-medium">{example.style} Transformation</h3>
                      <p className="text-sm text-gray-500">Generated with GPT-4</p>
                    </div>
                    <button className="text-sm font-medium text-ghibli-600 hover:text-ghibli-700">
                      Try This Style
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;
