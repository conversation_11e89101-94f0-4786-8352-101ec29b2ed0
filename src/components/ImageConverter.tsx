import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Upload, Download, <PERSON><PERSON>, <PERSON>rk<PERSON>, Camera } from "lucide-react";
import { toast } from "sonner";

export const styleOptions = [
  { value: "grayscale", label: "Grayscale", description: "Classic black and white" },
  { value: "sepia", label: "Sepia", description: "Vintage warm tone" },
  { value: "sketch", label: "Sketch", description: "Pencil drawing effect" },
  { value: "vintage", label: "Vintage", description: "Retro film look" },
  { value: "vibrant", label: "Vibrant", description: "Enhanced colors" },
  { value: "blur", label: "Artistic Blur", description: "Soft focus effect" },
  { value: "ghibli", label: "Ghibli Style", description: "Anime-inspired soft colors" },
  { value: "actionhero", label: "Action Hero", description: "High contrast cinematic look" }
];

// Function to parse natural language prompts and determine the appropriate style
export const parsePromptToStyle = (prompt: string): string | null => {
  const lowerPrompt = prompt.toLowerCase();

  // Ghibli style keywords
  if (lowerPrompt.includes('ghibli') || lowerPrompt.includes('anime') || lowerPrompt.includes('studio ghibli') ||
      lowerPrompt.includes('spirited away') || lowerPrompt.includes('totoro') || lowerPrompt.includes('miyazaki')) {
    return 'ghibli';
  }

  // Action hero style keywords
  if (lowerPrompt.includes('action hero') || lowerPrompt.includes('superhero') || lowerPrompt.includes('comic book') ||
      lowerPrompt.includes('cinematic') || lowerPrompt.includes('dramatic') || lowerPrompt.includes('hero style') ||
      lowerPrompt.includes('marvel') || lowerPrompt.includes('dc comics') || lowerPrompt.includes('batman') ||
      lowerPrompt.includes('superman') || lowerPrompt.includes('high contrast')) {
    return 'actionhero';
  }

  // Grayscale keywords
  if (lowerPrompt.includes('grayscale') || lowerPrompt.includes('black and white') || lowerPrompt.includes('monochrome') ||
      lowerPrompt.includes('gray scale') || lowerPrompt.includes('b&w') || lowerPrompt.includes('greyscale')) {
    return 'grayscale';
  }

  // Sepia keywords
  if (lowerPrompt.includes('sepia') || lowerPrompt.includes('brown tone') || lowerPrompt.includes('warm brown') ||
      lowerPrompt.includes('old photo') || lowerPrompt.includes('antique')) {
    return 'sepia';
  }

  // Sketch keywords
  if (lowerPrompt.includes('sketch') || lowerPrompt.includes('pencil') || lowerPrompt.includes('drawing') ||
      lowerPrompt.includes('line art') || lowerPrompt.includes('charcoal') || lowerPrompt.includes('artistic drawing')) {
    return 'sketch';
  }

  // Vintage keywords
  if (lowerPrompt.includes('vintage') || lowerPrompt.includes('retro') || lowerPrompt.includes('old film') ||
      lowerPrompt.includes('film look') || lowerPrompt.includes('nostalgic') || lowerPrompt.includes('classic')) {
    return 'vintage';
  }

  // Vibrant keywords
  if (lowerPrompt.includes('vibrant') || lowerPrompt.includes('bright') || lowerPrompt.includes('colorful') ||
      lowerPrompt.includes('saturated') || lowerPrompt.includes('vivid') || lowerPrompt.includes('enhanced colors') ||
      lowerPrompt.includes('pop colors') || lowerPrompt.includes('bold colors')) {
    return 'vibrant';
  }

  // Blur keywords
  if (lowerPrompt.includes('blur') || lowerPrompt.includes('soft focus') || lowerPrompt.includes('dreamy') ||
      lowerPrompt.includes('artistic blur') || lowerPrompt.includes('gaussian blur') || lowerPrompt.includes('soft')) {
    return 'blur';
  }

  return null;
};

export const applyImageFilter = (canvas: HTMLCanvasElement, ctx: CanvasRenderingContext2D, style: string) => {
  try {
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

  switch (style) {
    case "grayscale":
      for (let i = 0; i < data.length; i += 4) {
        const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
        data[i] = gray;
        data[i + 1] = gray;
        data[i + 2] = gray;
      }
      break;
    case "sepia":
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        data[i] = Math.min(255, r * 0.393 + g * 0.769 + b * 0.189);
        data[i + 1] = Math.min(255, r * 0.349 + g * 0.686 + b * 0.168);
        data[i + 2] = Math.min(255, r * 0.272 + g * 0.534 + b * 0.131);
      }
      break;
    case "sketch":
      // Enhanced sketch effect with edge detection
      for (let i = 0; i < data.length; i += 4) {
        const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
        const inverted = 255 - gray;
        const sketched = Math.min(255, inverted * 1.2);
        data[i] = sketched;
        data[i + 1] = sketched;
        data[i + 2] = sketched;
      }
      break;
    case "vintage":
      for (let i = 0; i < data.length; i += 4) {
        data[i] = Math.min(255, data[i] * 1.2 + 20);
        data[i + 1] = Math.min(255, data[i + 1] * 1.1 + 10);
        data[i + 2] = Math.max(0, data[i + 2] * 0.8 - 10);
      }
      break;
    case "vibrant":
      for (let i = 0; i < data.length; i += 4) {
        data[i] = Math.min(255, data[i] * 1.3);
        data[i + 1] = Math.min(255, data[i + 1] * 1.3);
        data[i + 2] = Math.min(255, data[i + 2] * 1.3);
      }
      break;
    case "blur":
      // Simple blur effect by averaging neighboring pixels
      const originalData = new Uint8ClampedArray(data);
      const width = canvas.width;
      const height = canvas.height;
      
      for (let y = 1; y < height - 1; y++) {
        for (let x = 1; x < width - 1; x++) {
          const idx = (y * width + x) * 4;
          let r = 0, g = 0, b = 0;
          
          // Average 3x3 neighborhood
          for (let dy = -1; dy <= 1; dy++) {
            for (let dx = -1; dx <= 1; dx++) {
              const neighborIdx = ((y + dy) * width + (x + dx)) * 4;
              r += originalData[neighborIdx];
              g += originalData[neighborIdx + 1];
              b += originalData[neighborIdx + 2];
            }
          }
          
          data[idx] = r / 9;
          data[idx + 1] = g / 9;
          data[idx + 2] = b / 9;
        }
      }
      break;
    case "ghibli":
      // Ghibli-style transformation: soft pastels, reduced contrast, warm tones
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        
        // Apply soft pastel effect with warm undertones
        data[i] = Math.min(255, r * 0.9 + 30);     // Slightly reduce red, add warmth
        data[i + 1] = Math.min(255, g * 0.95 + 20); // Soften green
        data[i + 2] = Math.min(255, b * 0.85 + 40); // Reduce blue, add yellow tint
        
        // Reduce overall contrast for dreamy effect
        const brightness = (data[i] + data[i + 1] + data[i + 2]) / 3;
        const factor = 0.3;
        data[i] = data[i] * (1 - factor) + brightness * factor;
        data[i + 1] = data[i + 1] * (1 - factor) + brightness * factor;
        data[i + 2] = data[i + 2] * (1 - factor) + brightness * factor;
      }
      break;
    case "actionhero":
      // Action Hero transformation: high contrast, desaturated colors, dramatic shadows
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        
        // Increase contrast dramatically
        const contrast = 1.5;
        const factor = (259 * (contrast + 255)) / (255 * (259 - contrast));
        
        let newR = factor * (r - 128) + 128;
        let newG = factor * (g - 128) + 128;
        let newB = factor * (b - 128) + 128;
        
        // Clamp values
        newR = Math.max(0, Math.min(255, newR));
        newG = Math.max(0, Math.min(255, newG));
        newB = Math.max(0, Math.min(255, newB));
        
        // Desaturate slightly and add blue tint for cinematic effect
        const gray = newR * 0.299 + newG * 0.587 + newB * 0.114;
        const saturation = 0.7;
        
        data[i] = Math.min(255, gray * (1 - saturation) + newR * saturation - 10);
        data[i + 1] = Math.min(255, gray * (1 - saturation) + newG * saturation - 5);
        data[i + 2] = Math.min(255, gray * (1 - saturation) + newB * saturation + 15);
      }
      break;
    default:
      console.warn(`Unknown filter style: ${style}`);
      throw new Error(`Unsupported filter style: ${style}`);
  }

  ctx.putImageData(imageData, 0, 0);
  } catch (error) {
    console.error(`Error applying ${style} filter:`, error);
    throw error;
  }
};

const ImageConverter = () => {
  const [originalImage, setOriginalImage] = useState<string | null>(null);
  const [convertedImage, setConvertedImage] = useState<string | null>(null);
  const [transformPrompt, setTransformPrompt] = useState<string>("");
  const [detectedStyle, setDetectedStyle] = useState<string>("");
  const [isConverting, setIsConverting] = useState(false);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setOriginalImage(e.target?.result as string);
        setConvertedImage(null);
      };
      reader.readAsDataURL(file);
      toast.success("Image uploaded successfully!");
    } else {
      toast.error("Please select a valid image file");
    }
  };

  const convertImage = async () => {
    if (!originalImage || !transformPrompt.trim()) {
      toast.error("Please upload an image and describe your desired transformation");
      return;
    }

    const parsedStyle = parsePromptToStyle(transformPrompt);
    if (!parsedStyle) {
      toast.error("I couldn't understand your transformation request. Try describing it differently (e.g., 'convert to Ghibli anime style', 'make it grayscale', 'apply sepia effect')");
      return;
    }

    setDetectedStyle(parsedStyle);
    setIsConverting(true);

    try {
      const img = new Image();

      // Handle image loading with proper async/await pattern
      await new Promise<void>((resolve, reject) => {
        img.onload = () => {
          try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            if (!ctx) {
              reject(new Error("Failed to get canvas context"));
              return;
            }

            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);

            applyImageFilter(canvas, ctx, parsedStyle);

            const convertedDataUrl = canvas.toDataURL('image/png');
            setConvertedImage(convertedDataUrl);

            const styleName = styleOptions.find(s => s.value === parsedStyle)?.label || parsedStyle;
            toast.success(`Image transformed to ${styleName} style!`);
            resolve();
          } catch (error) {
            reject(error);
          }
        };

        img.onerror = () => {
          reject(new Error("Failed to load image"));
        };

        img.src = originalImage;
      });

    } catch (error) {
      console.error('Error transforming image:', error);
      toast.error(`Failed to transform image: ${error.message}`);
    } finally {
      setIsConverting(false);
    }
  };

  const downloadImage = () => {
    if (!convertedImage) {
      toast.error("No transformed image to download");
      return;
    }

    try {
      const link = document.createElement('a');
      link.href = convertedImage;
      link.download = `transformed-${detectedStyle || 'image'}-${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success("Image downloaded successfully!");
    } catch (error) {
      console.error('Error downloading image:', error);
      toast.error("Failed to download image");
    }
  };

  return (
    <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
      <CardHeader className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-t-lg">
        <CardTitle className="flex items-center gap-2">
          <Camera className="h-5 w-5 text-purple-600" />
          AI-Powered Image Style Transformation
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6 space-y-6">
        {/* Upload Section */}
        <div className="space-y-3">
          <Label htmlFor="image-upload" className="text-base font-medium">Upload Your Image</Label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-purple-400 transition-colors">
            <input
              id="image-upload"
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
            />
            <Label htmlFor="image-upload" className="cursor-pointer">
              <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm text-gray-600">Click to upload or drag and drop</p>
              <p className="text-xs text-gray-400 mt-1">PNG, JPG, WEBP up to 10MB</p>
            </Label>
          </div>
        </div>

        {/* Transformation Prompt */}
        <div className="space-y-3">
          <Label htmlFor="transform-prompt" className="text-base font-medium">Describe Your Transformation</Label>
          <Textarea
            id="transform-prompt"
            placeholder="Convert to Ghibli anime style, make it look like an action hero, apply sepia vintage effect, turn into grayscale, make it more vibrant..."
            value={transformPrompt}
            onChange={(e) => setTransformPrompt(e.target.value)}
            rows={3}
            className="resize-none border-gray-200 focus:border-purple-400 focus:ring-purple-400"
          />
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <Sparkles className="h-3 w-3" />
            <span>Try: "Ghibli anime style", "action hero look", "vintage sepia", "black and white", "sketch effect"</span>
          </div>
        </div>

        {/* Convert Button */}
        <Button
          onClick={convertImage}
          disabled={!originalImage || !transformPrompt.trim() || isConverting || !parsePromptToStyle(transformPrompt)}
          className="w-full h-12 text-base font-medium bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isConverting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Transforming Image...
            </>
          ) : !originalImage ? (
            <>
              <Upload className="h-4 w-4 mr-2" />
              Upload Image First
            </>
          ) : !transformPrompt.trim() ? (
            <>
              <Palette className="h-4 w-4 mr-2" />
              Describe Transformation
            </>
          ) : !parsePromptToStyle(transformPrompt) ? (
            <>
              <Palette className="h-4 w-4 mr-2" />
              Style Not Recognized
            </>
          ) : (
            <>
              <Palette className="h-4 w-4 mr-2" />
              Apply AI Transformation
            </>
          )}
        </Button>

        {/* AI Prompt Analysis */}
        {transformPrompt.trim() && (
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Sparkles className="h-4 w-4 text-purple-600" />
              <span className="font-medium text-purple-800">
                AI Prompt Analysis
              </span>
            </div>
            {(() => {
              const detectedStyleFromPrompt = parsePromptToStyle(transformPrompt);
              if (detectedStyleFromPrompt) {
                const styleName = styleOptions.find(s => s.value === detectedStyleFromPrompt)?.label;
                return (
                  <div className="space-y-2">
                    <p className="text-sm text-green-700 font-medium">
                      ✓ Detected transformation: {styleName}
                    </p>
                    <p className="text-sm text-gray-600">
                      {detectedStyleFromPrompt === "ghibli" && "Creates a soft, dreamy anime-inspired look with warm pastels and reduced contrast."}
                      {detectedStyleFromPrompt === "actionhero" && "Applies high contrast with desaturated colors for a dramatic cinematic effect."}
                      {detectedStyleFromPrompt === "grayscale" && "Converts your image to classic black and white tones."}
                      {detectedStyleFromPrompt === "sepia" && "Adds vintage warmth with brown-toned coloring."}
                      {detectedStyleFromPrompt === "sketch" && "Transforms your photo into a pencil drawing style."}
                      {detectedStyleFromPrompt === "vintage" && "Creates a retro film look with enhanced warm tones."}
                      {detectedStyleFromPrompt === "vibrant" && "Boosts color saturation for more vivid results."}
                      {detectedStyleFromPrompt === "blur" && "Applies artistic blur for a soft focus effect."}
                    </p>
                    <div className="mt-2 pt-2 border-t border-purple-200">
                      <p className="text-xs text-purple-600">
                        Ready to transform! Click "Apply Transformation" to process your image.
                      </p>
                    </div>
                  </div>
                );
              } else {
                return (
                  <div className="space-y-2">
                    <p className="text-sm text-amber-700">
                      ⚠ I couldn't detect a specific transformation style.
                    </p>
                    <p className="text-xs text-amber-600">
                      Try being more specific (e.g., "Ghibli anime style", "black and white", "vintage sepia", "make it vibrant").
                    </p>
                  </div>
                );
              }
            })()}
          </div>
        )}

        {/* Image Preview */}
        <div className="grid md:grid-cols-2 gap-4">
          {originalImage && (
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Original Image</Label>
              <div className="relative rounded-lg overflow-hidden shadow-md">
                <img src={originalImage} alt="Original" className="w-full h-48 object-cover" />
              </div>
            </div>
          )}
          
          {convertedImage && (
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                Transformed ({styleOptions.find(s => s.value === detectedStyle)?.label || detectedStyle})
              </Label>
              <div className="relative rounded-lg overflow-hidden shadow-md">
                <img src={convertedImage} alt="Converted" className="w-full h-48 object-cover" />
              </div>
              <Button
                onClick={downloadImage}
                variant="outline"
                className="w-full border-2 border-purple-200 hover:border-purple-400 hover:bg-purple-50"
              >
                <Download className="h-4 w-4 mr-2" />
                Download Transformed Image
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ImageConverter;
