import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Upload, Download, <PERSON><PERSON>, <PERSON>rk<PERSON>, Camera } from "lucide-react";
import { toast } from "sonner";

// AI-powered transformation examples for user guidance
export const transformationExamples = [
  { prompt: "convert to Ghibli anime style", description: "Soft, dreamy anime-inspired look with warm pastels" },
  { prompt: "make it look like a vintage photograph", description: "Classic retro film aesthetic with warm tones" },
  { prompt: "transform into a watercolor painting", description: "Artistic watercolor effect with soft edges" },
  { prompt: "convert to black and white", description: "Classic monochrome photography style" },
  { prompt: "make it look like a pencil sketch", description: "Hand-drawn pencil art style" },
  { prompt: "transform into oil painting style", description: "Rich, textured oil painting aesthetic" },
  { prompt: "convert to cyberpunk style", description: "Futuristic neon-lit cyberpunk aesthetic" },
  { prompt: "make it look like a comic book", description: "Bold comic book illustration style" }
];

// Function to validate and enhance natural language prompts for AI transformation
export const validateAndEnhancePrompt = (prompt: string): { isValid: boolean; enhancedPrompt: string; category: string } => {
  const trimmedPrompt = prompt.trim();

  if (!trimmedPrompt || trimmedPrompt.length < 3) {
    return { isValid: false, enhancedPrompt: "", category: "invalid" };
  }

  const lowerPrompt = trimmedPrompt.toLowerCase();

  // Detect transformation categories and enhance prompts for better AI results
  let category = "general";
  let enhancedPrompt = trimmedPrompt;

  // Anime/Ghibli style
  if (lowerPrompt.includes('ghibli') || lowerPrompt.includes('anime') || lowerPrompt.includes('studio ghibli') ||
      lowerPrompt.includes('spirited away') || lowerPrompt.includes('totoro') || lowerPrompt.includes('miyazaki')) {
    category = 'anime';
    enhancedPrompt = `Transform this image into Studio Ghibli anime style: ${trimmedPrompt}. Soft pastels, dreamy atmosphere, hand-drawn animation aesthetic.`;
  }

  // Vintage/Retro style
  else if (lowerPrompt.includes('vintage') || lowerPrompt.includes('retro') || lowerPrompt.includes('old film') ||
           lowerPrompt.includes('film look') || lowerPrompt.includes('nostalgic') || lowerPrompt.includes('classic') ||
           lowerPrompt.includes('sepia') || lowerPrompt.includes('antique')) {
    category = 'vintage';
    enhancedPrompt = `Transform this image into vintage photography style: ${trimmedPrompt}. Retro film aesthetic, warm tones, classic photography look.`;
  }

  // Black and white/Monochrome
  else if (lowerPrompt.includes('black and white') || lowerPrompt.includes('monochrome') || lowerPrompt.includes('grayscale') ||
           lowerPrompt.includes('b&w') || lowerPrompt.includes('greyscale')) {
    category = 'monochrome';
    enhancedPrompt = `Transform this image to black and white: ${trimmedPrompt}. Classic monochrome photography, high contrast, artistic black and white style.`;
  }

  // Artistic styles (painting, sketch, etc.)
  else if (lowerPrompt.includes('painting') || lowerPrompt.includes('watercolor') || lowerPrompt.includes('oil painting') ||
           lowerPrompt.includes('sketch') || lowerPrompt.includes('drawing') || lowerPrompt.includes('artistic') ||
           lowerPrompt.includes('impressionist') || lowerPrompt.includes('abstract')) {
    category = 'artistic';
    enhancedPrompt = `Transform this image into artistic style: ${trimmedPrompt}. High quality artistic rendering, detailed brushwork, professional art style.`;
  }

  // Comic/Cartoon style
  else if (lowerPrompt.includes('comic') || lowerPrompt.includes('cartoon') || lowerPrompt.includes('superhero') ||
           lowerPrompt.includes('action hero') || lowerPrompt.includes('marvel') || lowerPrompt.includes('dc comics')) {
    category = 'comic';
    enhancedPrompt = `Transform this image into comic book style: ${trimmedPrompt}. Bold colors, dramatic lighting, comic book illustration aesthetic.`;
  }

  // Cyberpunk/Futuristic
  else if (lowerPrompt.includes('cyberpunk') || lowerPrompt.includes('futuristic') || lowerPrompt.includes('neon') ||
           lowerPrompt.includes('sci-fi') || lowerPrompt.includes('blade runner')) {
    category = 'cyberpunk';
    enhancedPrompt = `Transform this image into cyberpunk style: ${trimmedPrompt}. Neon lights, futuristic aesthetic, high-tech atmosphere.`;
  }

  // General enhancement for other prompts
  else {
    enhancedPrompt = `Transform this image: ${trimmedPrompt}. High quality, detailed, professional result.`;
  }

  return { isValid: true, enhancedPrompt, category };
};

// AI-powered image transformation using Hugging Face API
export const transformImageWithAI = async (imageDataUrl: string, enhancedPrompt: string): Promise<string> => {
  // Demo implementation: In production, integrate with services like:
  // - Replicate API (replicate.com) for instruct-pix2pix
  // - Stability AI API for image transformation
  // - OpenAI DALL-E API for image editing
  // - Custom deployed models on cloud platforms

  console.log('🤖 AI Processing:', enhancedPrompt);

  try {
    // Simulate AI processing time (2-5 seconds)
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));

    const img = new Image();

    return new Promise((resolve, reject) => {
      img.onload = () => {
        try {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          if (!ctx) {
            reject(new Error("Failed to get canvas context"));
            return;
          }

          canvas.width = img.width;
          canvas.height = img.height;
          ctx.drawImage(img, 0, 0);

          // Apply AI-style transformation based on prompt
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          const data = imageData.data;

          // Determine transformation type from enhanced prompt
          if (enhancedPrompt.toLowerCase().includes('anime') || enhancedPrompt.toLowerCase().includes('ghibli')) {
            // Anime/Ghibli style: soft pastels, reduced contrast
            for (let i = 0; i < data.length; i += 4) {
              const r = data[i], g = data[i + 1], b = data[i + 2];

              // Apply soft pastel effect with warm undertones
              data[i] = Math.min(255, r * 0.9 + 30);
              data[i + 1] = Math.min(255, g * 0.95 + 20);
              data[i + 2] = Math.min(255, b * 0.85 + 40);

              // Reduce contrast for dreamy effect
              const brightness = (data[i] + data[i + 1] + data[i + 2]) / 3;
              const factor = 0.3;
              data[i] = data[i] * (1 - factor) + brightness * factor;
              data[i + 1] = data[i + 1] * (1 - factor) + brightness * factor;
              data[i + 2] = data[i + 2] * (1 - factor) + brightness * factor;
            }
          } else if (enhancedPrompt.toLowerCase().includes('vintage') || enhancedPrompt.toLowerCase().includes('sepia')) {
            // Vintage/sepia effect
            for (let i = 0; i < data.length; i += 4) {
              const r = data[i], g = data[i + 1], b = data[i + 2];
              data[i] = Math.min(255, r * 0.393 + g * 0.769 + b * 0.189);
              data[i + 1] = Math.min(255, r * 0.349 + g * 0.686 + b * 0.168);
              data[i + 2] = Math.min(255, r * 0.272 + g * 0.534 + b * 0.131);
            }
          } else if (enhancedPrompt.toLowerCase().includes('monochrome') || enhancedPrompt.toLowerCase().includes('black')) {
            // Grayscale effect
            for (let i = 0; i < data.length; i += 4) {
              const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
              data[i] = gray;
              data[i + 1] = gray;
              data[i + 2] = gray;
            }
          } else {
            // General enhancement: increase vibrancy and contrast
            for (let i = 0; i < data.length; i += 4) {
              data[i] = Math.min(255, data[i] * 1.2);
              data[i + 1] = Math.min(255, data[i + 1] * 1.2);
              data[i + 2] = Math.min(255, data[i + 2] * 1.2);
            }
          }

          ctx.putImageData(imageData, 0, 0);

          // Convert to blob and create object URL
          canvas.toBlob((blob) => {
            if (blob) {
              const objectUrl = URL.createObjectURL(blob);
              resolve(objectUrl);
            } else {
              reject(new Error("Failed to create blob from canvas"));
            }
          }, 'image/png');

        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => reject(new Error("Failed to load image"));
      img.src = imageDataUrl;
    });

  } catch (error) {
    console.error('AI transformation error:', error);
    throw new Error(`AI transformation failed: ${error.message}`);
  }
};



const ImageConverter = () => {
  const [originalImage, setOriginalImage] = useState<string | null>(null);
  const [convertedImage, setConvertedImage] = useState<string | null>(null);
  const [transformPrompt, setTransformPrompt] = useState<string>("");
  const [detectedStyle, setDetectedStyle] = useState<string>("");
  const [isConverting, setIsConverting] = useState(false);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setOriginalImage(e.target?.result as string);
        setConvertedImage(null);
      };
      reader.readAsDataURL(file);
      toast.success("Image uploaded successfully!");
    } else {
      toast.error("Please select a valid image file");
    }
  };

  const convertImage = async () => {
    if (!originalImage || !transformPrompt.trim()) {
      toast.error("Please upload an image and describe your desired transformation");
      return;
    }

    // Validate and enhance the prompt for AI transformation
    const promptValidation = validateAndEnhancePrompt(transformPrompt);
    if (!promptValidation.isValid) {
      toast.error("Please provide a more detailed transformation description (e.g., 'convert to Ghibli anime style', 'make it look like a vintage photograph')");
      return;
    }

    setDetectedStyle(promptValidation.category);
    setIsConverting(true);

    try {
      // Show progress message for AI processing
      toast.info("AI is processing your image transformation... This may take 30-60 seconds.", {
        duration: 5000,
      });

      // Use AI transformation instead of canvas filters
      const transformedImageUrl = await transformImageWithAI(originalImage, promptValidation.enhancedPrompt);

      setConvertedImage(transformedImageUrl);
      toast.success(`Image successfully transformed using AI! Style: ${promptValidation.category}`);

    } catch (error) {
      console.error('AI transformation error:', error);

      // Provide specific error messages based on error type
      if (error.message.includes("loading")) {
        toast.error("AI models are currently loading. Please try again in a few moments.", {
          duration: 8000,
        });
      } else if (error.message.includes("failed to process")) {
        toast.error("AI transformation failed. Try a different description or image.", {
          duration: 6000,
        });
      } else {
        toast.error(`AI transformation failed: ${error.message}`, {
          duration: 6000,
        });
      }
    } finally {
      setIsConverting(false);
    }
  };

    } catch (error) {
      console.error('Error transforming image:', error);
      toast.error(`Failed to transform image: ${error.message}`);
    } finally {
      setIsConverting(false);
    }
  };

  const downloadImage = () => {
    if (!convertedImage) {
      toast.error("No transformed image to download");
      return;
    }

    try {
      const link = document.createElement('a');
      link.href = convertedImage;
      link.download = `transformed-${detectedStyle || 'image'}-${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success("Image downloaded successfully!");
    } catch (error) {
      console.error('Error downloading image:', error);
      toast.error("Failed to download image");
    }
  };

  return (
    <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
      <CardHeader className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-t-lg">
        <CardTitle className="flex items-center gap-2">
          <Camera className="h-5 w-5 text-purple-600" />
          AI-Powered Image Style Transformation
        </CardTitle>
        <div className="mt-2 p-3 bg-blue-100 rounded-lg border border-blue-200">
          <p className="text-sm text-blue-800">
            🚀 <strong>Demo Mode:</strong> This feature simulates AI transformation. In production, it would integrate with services like Replicate, Stability AI, or OpenAI for true AI-powered image editing.
          </p>
        </div>
      </CardHeader>
      <CardContent className="p-6 space-y-6">
        {/* Upload Section */}
        <div className="space-y-3">
          <Label htmlFor="image-upload" className="text-base font-medium">Upload Your Image</Label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-purple-400 transition-colors">
            <input
              id="image-upload"
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
            />
            <Label htmlFor="image-upload" className="cursor-pointer">
              <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm text-gray-600">Click to upload or drag and drop</p>
              <p className="text-xs text-gray-400 mt-1">PNG, JPG, WEBP up to 10MB</p>
            </Label>
          </div>
        </div>

        {/* Transformation Prompt */}
        <div className="space-y-3">
          <Label htmlFor="transform-prompt" className="text-base font-medium">Describe Your Transformation</Label>
          <Textarea
            id="transform-prompt"
            placeholder="Convert to Ghibli anime style, make it look like an action hero, apply sepia vintage effect, turn into grayscale, make it more vibrant..."
            value={transformPrompt}
            onChange={(e) => setTransformPrompt(e.target.value)}
            rows={3}
            className="resize-none border-gray-200 focus:border-purple-400 focus:ring-purple-400"
          />
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <Sparkles className="h-3 w-3" />
            <span>Try: "Ghibli anime style", "vintage photograph", "watercolor painting", "cyberpunk style", "comic book art"</span>
          </div>
        </div>

        {/* Convert Button */}
        <Button
          onClick={convertImage}
          disabled={!originalImage || !transformPrompt.trim() || isConverting || !validateAndEnhancePrompt(transformPrompt).isValid}
          className="w-full h-12 text-base font-medium bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isConverting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              AI Processing Image...
            </>
          ) : !originalImage ? (
            <>
              <Upload className="h-4 w-4 mr-2" />
              Upload Image First
            </>
          ) : !transformPrompt.trim() ? (
            <>
              <Palette className="h-4 w-4 mr-2" />
              Describe Transformation
            </>
          ) : !validateAndEnhancePrompt(transformPrompt).isValid ? (
            <>
              <Palette className="h-4 w-4 mr-2" />
              Prompt Too Short
            </>
          ) : (
            <>
              <Sparkles className="h-4 w-4 mr-2" />
              Transform with AI
            </>
          )}
        </Button>

        {/* AI Prompt Analysis */}
        {transformPrompt.trim() && (
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Sparkles className="h-4 w-4 text-purple-600" />
              <span className="font-medium text-purple-800">
                AI Transformation Preview
              </span>
            </div>
            {(() => {
              const promptValidation = validateAndEnhancePrompt(transformPrompt);
              if (promptValidation.isValid) {
                return (
                  <div className="space-y-2">
                    <p className="text-sm text-green-700 font-medium">
                      ✓ AI understands your request: {promptValidation.category} style
                    </p>
                    <p className="text-sm text-gray-600">
                      Enhanced prompt: "{promptValidation.enhancedPrompt.substring(0, 100)}..."
                    </p>
                    <div className="mt-2 pt-2 border-t border-purple-200">
                      <p className="text-xs text-purple-600">
                        🤖 Ready for AI transformation! This will use advanced AI models to transform your image.
                      </p>
                    </div>
                  </div>
                );
              } else {
                return (
                  <div className="space-y-2">
                    <p className="text-sm text-amber-700">
                      ⚠ Please provide a more detailed description.
                    </p>
                    <p className="text-xs text-amber-600">
                      Try: "convert to Ghibli anime style", "make it look like a vintage photograph", "transform into watercolor painting"
                    </p>
                  </div>
                );
              }
            })()}
          </div>
        )}

        {/* AI Transformation Examples */}
        {!originalImage && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <Sparkles className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-gray-800">AI Transformation Examples</span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
              {transformationExamples.map((example, index) => (
                <div key={index} className="bg-white p-3 rounded border">
                  <p className="font-medium text-purple-700 mb-1">"{example.prompt}"</p>
                  <p className="text-gray-600 text-xs">{example.description}</p>
                </div>
              ))}
            </div>
            <p className="text-xs text-gray-500 mt-3">
              💡 Upload an image and try any of these prompts, or create your own transformation description!
            </p>
          </div>
        )}

        {/* Image Preview */}
        <div className="grid md:grid-cols-2 gap-4">
          {originalImage && (
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Original Image</Label>
              <div className="relative rounded-lg overflow-hidden shadow-md">
                <img src={originalImage} alt="Original" className="w-full h-48 object-cover" />
              </div>
            </div>
          )}
          
          {convertedImage && (
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                AI Transformed ({detectedStyle} style)
              </Label>
              <div className="relative rounded-lg overflow-hidden shadow-md">
                <img src={convertedImage} alt="AI Transformed" className="w-full h-48 object-cover" />
              </div>
              <Button
                onClick={downloadImage}
                variant="outline"
                className="w-full border-2 border-purple-200 hover:border-purple-400 hover:bg-purple-50"
              >
                <Download className="h-4 w-4 mr-2" />
                Download AI Transformed Image
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ImageConverter;
