import { useState } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Upload, Download, Palette, Sparkles, Camera } from "lucide-react";
import { toast } from "sonner";

export const styleOptions = [
  { value: "grayscale", label: "Grayscale", description: "Classic black and white" },
  { value: "sepia", label: "Sepia", description: "Vintage warm tone" },
  { value: "sketch", label: "Sketch", description: "Pencil drawing effect" },
  { value: "vintage", label: "Vintage", description: "Retro film look" },
  { value: "vibrant", label: "Vibrant", description: "Enhanced colors" },
  { value: "blur", label: "Artistic Blur", description: "Soft focus effect" },
  { value: "ghibli", label: "Ghibli Style", description: "Anime-inspired soft colors" },
  { value: "actionhero", label: "Action Hero", description: "High contrast cinematic look" }
];

export const applyImageFilter = (canvas: HTMLCanvasElement, ctx: CanvasRenderingContext2D, style: string) => {
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageData.data;

  switch (style) {
    case "grayscale":
      for (let i = 0; i < data.length; i += 4) {
        const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
        data[i] = gray;
        data[i + 1] = gray;
        data[i + 2] = gray;
      }
      break;
    case "sepia":
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        data[i] = Math.min(255, r * 0.393 + g * 0.769 + b * 0.189);
        data[i + 1] = Math.min(255, r * 0.349 + g * 0.686 + b * 0.168);
        data[i + 2] = Math.min(255, r * 0.272 + g * 0.534 + b * 0.131);
      }
      break;
    case "sketch":
      // Enhanced sketch effect with edge detection
      for (let i = 0; i < data.length; i += 4) {
        const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
        const inverted = 255 - gray;
        const sketched = Math.min(255, inverted * 1.2);
        data[i] = sketched;
        data[i + 1] = sketched;
        data[i + 2] = sketched;
      }
      break;
    case "vintage":
      for (let i = 0; i < data.length; i += 4) {
        data[i] = Math.min(255, data[i] * 1.2 + 20);
        data[i + 1] = Math.min(255, data[i + 1] * 1.1 + 10);
        data[i + 2] = Math.max(0, data[i + 2] * 0.8 - 10);
      }
      break;
    case "vibrant":
      for (let i = 0; i < data.length; i += 4) {
        data[i] = Math.min(255, data[i] * 1.3);
        data[i + 1] = Math.min(255, data[i + 1] * 1.3);
        data[i + 2] = Math.min(255, data[i + 2] * 1.3);
      }
      break;
    case "blur":
      // Simple blur effect by averaging neighboring pixels
      const originalData = new Uint8ClampedArray(data);
      const width = canvas.width;
      const height = canvas.height;
      
      for (let y = 1; y < height - 1; y++) {
        for (let x = 1; x < width - 1; x++) {
          const idx = (y * width + x) * 4;
          let r = 0, g = 0, b = 0;
          
          // Average 3x3 neighborhood
          for (let dy = -1; dy <= 1; dy++) {
            for (let dx = -1; dx <= 1; dx++) {
              const neighborIdx = ((y + dy) * width + (x + dx)) * 4;
              r += originalData[neighborIdx];
              g += originalData[neighborIdx + 1];
              b += originalData[neighborIdx + 2];
            }
          }
          
          data[idx] = r / 9;
          data[idx + 1] = g / 9;
          data[idx + 2] = b / 9;
        }
      }
      break;
    case "ghibli":
      // Ghibli-style transformation: soft pastels, reduced contrast, warm tones
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        
        // Apply soft pastel effect with warm undertones
        data[i] = Math.min(255, r * 0.9 + 30);     // Slightly reduce red, add warmth
        data[i + 1] = Math.min(255, g * 0.95 + 20); // Soften green
        data[i + 2] = Math.min(255, b * 0.85 + 40); // Reduce blue, add yellow tint
        
        // Reduce overall contrast for dreamy effect
        const brightness = (data[i] + data[i + 1] + data[i + 2]) / 3;
        const factor = 0.3;
        data[i] = data[i] * (1 - factor) + brightness * factor;
        data[i + 1] = data[i + 1] * (1 - factor) + brightness * factor;
        data[i + 2] = data[i + 2] * (1 - factor) + brightness * factor;
      }
      break;
    case "actionhero":
      // Action Hero transformation: high contrast, desaturated colors, dramatic shadows
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        
        // Increase contrast dramatically
        const contrast = 1.5;
        const factor = (259 * (contrast + 255)) / (255 * (259 - contrast));
        
        let newR = factor * (r - 128) + 128;
        let newG = factor * (g - 128) + 128;
        let newB = factor * (b - 128) + 128;
        
        // Clamp values
        newR = Math.max(0, Math.min(255, newR));
        newG = Math.max(0, Math.min(255, newG));
        newB = Math.max(0, Math.min(255, newB));
        
        // Desaturate slightly and add blue tint for cinematic effect
        const gray = newR * 0.299 + newG * 0.587 + newB * 0.114;
        const saturation = 0.7;
        
        data[i] = Math.min(255, gray * (1 - saturation) + newR * saturation - 10);
        data[i + 1] = Math.min(255, gray * (1 - saturation) + newG * saturation - 5);
        data[i + 2] = Math.min(255, gray * (1 - saturation) + newB * saturation + 15);
      }
      break;
  }

  ctx.putImageData(imageData, 0, 0);
};

const ImageConverter = () => {
  const [originalImage, setOriginalImage] = useState<string | null>(null);
  const [convertedImage, setConvertedImage] = useState<string | null>(null);
  const [selectedStyle, setSelectedStyle] = useState<string>("");
  const [isConverting, setIsConverting] = useState(false);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setOriginalImage(e.target?.result as string);
        setConvertedImage(null);
      };
      reader.readAsDataURL(file);
      toast.success("Image uploaded successfully!");
    } else {
      toast.error("Please select a valid image file");
    }
  };

  const convertImage = async () => {
    if (!originalImage || !selectedStyle) {
      toast.error("Please upload an image and select a style");
      return;
    }

    setIsConverting(true);
    try {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);

        applyImageFilter(canvas, ctx, selectedStyle);

        const convertedDataUrl = canvas.toDataURL('image/png');
        setConvertedImage(convertedDataUrl);
        toast.success(`Image converted to ${styleOptions.find(s => s.value === selectedStyle)?.label} style!`);
      };
      img.src = originalImage;
    } catch (error) {
      toast.error("Failed to convert image");
    } finally {
      setIsConverting(false);
    }
  };

  const downloadImage = () => {
    if (!convertedImage) return;
    
    const link = document.createElement('a');
    link.href = convertedImage;
    link.download = `converted-${selectedStyle}-${Date.now()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
      <CardHeader className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-t-lg">
        <CardTitle className="flex items-center gap-2">
          <Camera className="h-5 w-5 text-purple-600" />
          Enhanced Image Style Converter
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6 space-y-6">
        {/* Upload Section */}
        <div className="space-y-3">
          <Label htmlFor="image-upload" className="text-base font-medium">Upload Your Image</Label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-purple-400 transition-colors">
            <input
              id="image-upload"
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
            />
            <Label htmlFor="image-upload" className="cursor-pointer">
              <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm text-gray-600">Click to upload or drag and drop</p>
              <p className="text-xs text-gray-400 mt-1">PNG, JPG, WEBP up to 10MB</p>
            </Label>
          </div>
        </div>

        {/* Style Selection */}
        <div className="space-y-3">
          <Label className="text-base font-medium">Choose Transformation Style</Label>
          <Select value={selectedStyle} onValueChange={setSelectedStyle}>
            <SelectTrigger className="border-gray-200 focus:border-purple-400 focus:ring-purple-400">
              <SelectValue placeholder="Select a style transformation" />
            </SelectTrigger>
            <SelectContent>
              {styleOptions.map((style) => (
                <SelectItem key={style.value} value={style.value}>
                  <div className="flex flex-col">
                    <span className="font-medium">{style.label}</span>
                    <span className="text-xs text-gray-500">{style.description}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Convert Button */}
        <Button
          onClick={convertImage}
          disabled={!originalImage || !selectedStyle || isConverting}
          className="w-full h-12 text-base font-medium bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
        >
          {isConverting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Transforming Image...
            </>
          ) : (
            <>
              <Palette className="h-4 w-4 mr-2" />
              Apply Transformation
            </>
          )}
        </Button>

        {/* Style Information */}
        {selectedStyle && (
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Sparkles className="h-4 w-4 text-purple-600" />
              <span className="font-medium text-purple-800">
                {styleOptions.find(s => s.value === selectedStyle)?.label} Transformation
              </span>
            </div>
            <p className="text-sm text-gray-600">
              {selectedStyle === "ghibli" && "Creates a soft, dreamy anime-inspired look with warm pastels and reduced contrast."}
              {selectedStyle === "actionhero" && "Applies high contrast with desaturated colors for a dramatic cinematic effect."}
              {selectedStyle === "grayscale" && "Converts your image to classic black and white tones."}
              {selectedStyle === "sepia" && "Adds vintage warmth with brown-toned coloring."}
              {selectedStyle === "sketch" && "Transforms your photo into a pencil drawing style."}
              {selectedStyle === "vintage" && "Creates a retro film look with enhanced warm tones."}
              {selectedStyle === "vibrant" && "Boosts color saturation for more vivid results."}
              {selectedStyle === "blur" && "Applies artistic blur for a soft focus effect."}
            </p>
          </div>
        )}

        {/* Image Preview */}
        <div className="grid md:grid-cols-2 gap-4">
          {originalImage && (
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Original Image</Label>
              <div className="relative rounded-lg overflow-hidden shadow-md">
                <img src={originalImage} alt="Original" className="w-full h-48 object-cover" />
              </div>
            </div>
          )}
          
          {convertedImage && (
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                Transformed ({styleOptions.find(s => s.value === selectedStyle)?.label})
              </Label>
              <div className="relative rounded-lg overflow-hidden shadow-md">
                <img src={convertedImage} alt="Converted" className="w-full h-48 object-cover" />
              </div>
              <Button
                onClick={downloadImage}
                variant="outline"
                className="w-full border-2 border-purple-200 hover:border-purple-400 hover:bg-purple-50"
              >
                <Download className="h-4 w-4 mr-2" />
                Download Transformed Image
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ImageConverter;
