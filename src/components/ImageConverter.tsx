import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Upload, Download, <PERSON><PERSON>, <PERSON>rk<PERSON>, Camera } from "lucide-react";
import { toast } from "sonner";

// AI-powered transformation examples for user guidance
export const transformationExamples = [
  { prompt: "convert to Ghibli anime style", description: "Studio Ghibli animation with soft pastels and dreamy atmosphere" },
  { prompt: "make it look like a vintage photograph", description: "Classic retro film aesthetic with warm sepia tones" },
  { prompt: "transform into a watercolor painting", description: "Artistic watercolor with flowing colors and paper texture" },
  { prompt: "convert to oil painting style", description: "Rich textures with visible brushstrokes and vibrant colors" },
  { prompt: "make it action hero cinematic style", description: "Dramatic lighting with bold colors and movie poster aesthetic" },
  { prompt: "turn into comic book style", description: "Bold colors with clean lines and graphic novel appearance" },
  { prompt: "convert to cyberpunk style", description: "Neon lighting with futuristic high-tech atmosphere" },
  { prompt: "make it black and white", description: "High contrast monochrome with dramatic shadows" }
];

// Function to validate and enhance natural language prompts for AI transformation
export const validateAndEnhancePrompt = (prompt: string): { isValid: boolean; enhancedPrompt: string; category: string } => {
  const trimmedPrompt = prompt.trim();

  if (!trimmedPrompt || trimmedPrompt.length < 3) {
    return { isValid: false, enhancedPrompt: "", category: "invalid" };
  }

  const lowerPrompt = trimmedPrompt.toLowerCase();

  // Detect transformation categories and enhance prompts for better AI results
  let category = "general";
  let enhancedPrompt = trimmedPrompt;

  // Anime/Ghibli style - optimized for FLUX Kontext Pro
  if (lowerPrompt.includes('ghibli') || lowerPrompt.includes('anime') || lowerPrompt.includes('studio ghibli') ||
      lowerPrompt.includes('spirited away') || lowerPrompt.includes('totoro') || lowerPrompt.includes('miyazaki')) {
    category = 'anime';
    enhancedPrompt = `Convert this image to Studio Ghibli anime style with soft pastels, dreamy atmosphere, hand-drawn animation aesthetic, warm lighting, gentle watercolor-like textures, and whimsical details. Maintain the original composition while applying the distinctive Ghibli art style.`;
  }

  // Vintage/Retro style
  else if (lowerPrompt.includes('vintage') || lowerPrompt.includes('retro') || lowerPrompt.includes('old film') ||
           lowerPrompt.includes('film look') || lowerPrompt.includes('nostalgic') || lowerPrompt.includes('classic') ||
           lowerPrompt.includes('sepia') || lowerPrompt.includes('antique')) {
    category = 'vintage';
    enhancedPrompt = `Transform this image into a vintage photograph with classic retro film aesthetic, warm sepia tones, aged paper texture, nostalgic atmosphere, subtle film grain, and the characteristic look of old photographs from the 1970s-80s.`;
  }

  // Black and white/Monochrome
  else if (lowerPrompt.includes('black and white') || lowerPrompt.includes('monochrome') || lowerPrompt.includes('grayscale') ||
           lowerPrompt.includes('b&w') || lowerPrompt.includes('greyscale')) {
    category = 'monochrome';
    enhancedPrompt = `Convert this image to dramatic black and white with high contrast, professional monochrome photography aesthetic, rich shadows and highlights, and classic black and white film look.`;
  }

  // Watercolor painting style
  else if (lowerPrompt.includes('watercolor')) {
    category = 'artistic';
    enhancedPrompt = `Transform this image into a watercolor painting with soft flowing colors, artistic brushstrokes, paper texture, delicate color bleeding effects, and the characteristic look of professional watercolor artwork.`;
  }

  // Oil painting style
  else if (lowerPrompt.includes('oil painting') || lowerPrompt.includes('oil paint')) {
    category = 'artistic';
    enhancedPrompt = `Convert this image to an oil painting with rich textures, visible brushstrokes, thick paint application, vibrant colors, and the classic look of traditional oil painting artwork.`;
  }

  // Other artistic styles
  else if (lowerPrompt.includes('painting') || lowerPrompt.includes('sketch') || lowerPrompt.includes('drawing') ||
           lowerPrompt.includes('artistic') || lowerPrompt.includes('impressionist') || lowerPrompt.includes('abstract')) {
    category = 'artistic';
    enhancedPrompt = `Transform this image into artistic style: ${trimmedPrompt}. High quality artistic rendering, detailed brushwork, professional art style with rich textures and artistic details.`;
  }

  // Comic/Cartoon style
  else if (lowerPrompt.includes('comic') || lowerPrompt.includes('cartoon') || lowerPrompt.includes('superhero') ||
           lowerPrompt.includes('action hero') || lowerPrompt.includes('marvel') || lowerPrompt.includes('dc comics')) {
    category = 'comic';
    enhancedPrompt = `Transform this image into comic book style with bold colors, dramatic lighting, clean illustration lines, vibrant comic book aesthetic, and professional graphic novel appearance.`;
  }

  // Action hero/cinematic style
  else if (lowerPrompt.includes('action') || lowerPrompt.includes('hero') || lowerPrompt.includes('cinematic') ||
           lowerPrompt.includes('movie') || lowerPrompt.includes('epic')) {
    category = 'cinematic';
    enhancedPrompt = `Transform this image into action hero cinematic style with dramatic lighting, bold colors, movie poster aesthetic, high contrast, and epic film-like atmosphere.`;
  }

  // Cyberpunk/Futuristic
  else if (lowerPrompt.includes('cyberpunk') || lowerPrompt.includes('futuristic') || lowerPrompt.includes('neon') ||
           lowerPrompt.includes('sci-fi') || lowerPrompt.includes('blade runner')) {
    category = 'cyberpunk';
    enhancedPrompt = `Convert this image to cyberpunk style with neon lighting, futuristic aesthetic, high-tech atmosphere, electric blue and purple colors, and sci-fi movie lighting effects.`;
  }

  // General enhancement for other prompts
  else {
    enhancedPrompt = `Apply the following transformation to this image: ${trimmedPrompt}. Maintain high quality and professional results while preserving the original composition.`;
  }

  return { isValid: true, enhancedPrompt, category };
};

// Real AI-powered image transformation using Replicate's FLUX Kontext Pro API
export const transformImageWithAI = async (imageDataUrl: string, enhancedPrompt: string): Promise<string> => {
  const REPLICATE_API_TOKEN = import.meta.env.VITE_REPLICATE_API_TOKEN;

  if (!REPLICATE_API_TOKEN) {
    throw new Error('Replicate API token not configured. Please add VITE_REPLICATE_API_TOKEN to your environment variables.');
  }

  try {
    console.log('🤖 AI Processing with FLUX Kontext Pro:', enhancedPrompt);

    // Create prediction with Replicate API
    const predictionResponse = await fetch('https://api.replicate.com/v1/models/black-forest-labs/flux-kontext-pro/predictions', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${REPLICATE_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        input: {
          image: imageDataUrl,
          prompt: enhancedPrompt,
          num_inference_steps: 28,
          guidance_scale: 3.5,
          seed: Math.floor(Math.random() * 1000000),
        }
      })
    });

    if (!predictionResponse.ok) {
      const errorData = await predictionResponse.json();
      throw new Error(`Replicate API error: ${errorData.detail || predictionResponse.statusText}`);
    }

    const prediction = await predictionResponse.json();
    console.log('Prediction created:', prediction.id);

    // Poll for completion
    let result = prediction;
    while (result.status === 'starting' || result.status === 'processing') {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const statusResponse = await fetch(`https://api.replicate.com/v1/predictions/${result.id}`, {
        headers: {
          'Authorization': `Token ${REPLICATE_API_TOKEN}`,
        }
      });

      if (!statusResponse.ok) {
        throw new Error(`Failed to check prediction status: ${statusResponse.statusText}`);
      }

      result = await statusResponse.json();
      console.log('Prediction status:', result.status);
    }

    if (result.status === 'failed') {
      throw new Error(`AI transformation failed: ${result.error || 'Unknown error'}`);
    }

    if (result.status === 'succeeded' && result.output) {
      // Convert the output URL to data URL for consistency
      const outputResponse = await fetch(result.output);
      const outputBlob = await outputResponse.blob();

      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = () => reject(new Error('Failed to convert result to data URL'));
        reader.readAsDataURL(outputBlob);
      });
    }

    throw new Error('Unexpected prediction result');

  } catch (error) {
    console.error('AI transformation error:', error);
    throw error;
  }
};



const ImageConverter = () => {
  const [originalImage, setOriginalImage] = useState<string | null>(null);
  const [convertedImage, setConvertedImage] = useState<string | null>(null);
  const [transformPrompt, setTransformPrompt] = useState<string>("");
  const [detectedStyle, setDetectedStyle] = useState<string>("");
  const [isConverting, setIsConverting] = useState(false);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setOriginalImage(e.target?.result as string);
        setConvertedImage(null);
      };
      reader.readAsDataURL(file);
      toast.success("Image uploaded successfully!");
    } else {
      toast.error("Please select a valid image file");
    }
  };

  const convertImage = async () => {
    if (!originalImage || !transformPrompt.trim()) {
      toast.error("Please upload an image and describe your desired transformation");
      return;
    }

    // Validate and enhance the prompt for AI transformation
    const promptValidation = validateAndEnhancePrompt(transformPrompt);
    if (!promptValidation.isValid) {
      toast.error("Please provide a more detailed transformation description (e.g., 'convert to Ghibli anime style', 'make it look like a vintage photograph')");
      return;
    }

    setDetectedStyle(promptValidation.category);
    setIsConverting(true);

    try {
      // Show progress message for AI processing
      toast.info("AI is processing your image transformation... This may take 30-60 seconds.", {
        duration: 5000,
      });

      // Use AI transformation instead of canvas filters
      const transformedImageUrl = await transformImageWithAI(originalImage, promptValidation.enhancedPrompt);

      setConvertedImage(transformedImageUrl);
      toast.success(`Image successfully transformed using AI! Style: ${promptValidation.category}`);

    } catch (error) {
      console.error('AI transformation error:', error);

      // Provide specific error messages based on error type
      if (error.message.includes("loading")) {
        toast.error("AI models are currently loading. Please try again in a few moments.", {
          duration: 8000,
        });
      } else if (error.message.includes("failed to process")) {
        toast.error("AI transformation failed. Try a different description or image.", {
          duration: 6000,
        });
      } else {
        toast.error(`AI transformation failed: ${error.message}`, {
          duration: 6000,
        });
      }
    } finally {
      setIsConverting(false);
    }
  };

  const downloadImage = () => {
    if (!convertedImage) {
      toast.error("No transformed image to download");
      return;
    }

    try {
      const link = document.createElement('a');
      link.href = convertedImage;
      link.download = `transformed-${detectedStyle || 'image'}-${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success("Image downloaded successfully!");
    } catch (error) {
      console.error('Error downloading image:', error);
      toast.error("Failed to download image");
    }
  };

  return (
    <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
      <CardHeader className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-t-lg">
        <CardTitle className="flex items-center gap-2">
          <Camera className="h-5 w-5 text-purple-600" />
          AI-Powered Image Style Transformation
        </CardTitle>

      </CardHeader>
      <CardContent className="p-6 space-y-6">
        {/* API Configuration Notice */}
        {!import.meta.env.VITE_REPLICATE_API_TOKEN && (
          <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <div className="flex items-start gap-3">
              <Sparkles className="h-5 w-5 text-amber-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-amber-800">API Configuration Required</h4>
                <p className="text-sm text-amber-700 mt-1">
                  To use real AI-powered transformations, add your Replicate API token to the <code className="bg-amber-100 px-1 rounded">.env</code> file:
                </p>
                <p className="text-xs text-amber-600 mt-2 font-mono bg-amber-100 p-2 rounded">
                  VITE_REPLICATE_API_TOKEN=your_token_here
                </p>
                <p className="text-xs text-amber-600 mt-1">
                  Get your free API token at <a href="https://replicate.com/account/api-tokens" target="_blank" rel="noopener noreferrer" className="underline">replicate.com/account/api-tokens</a>
                </p>
              </div>
            </div>
          </div>
        )}
        {/* Upload Section */}
        <div className="space-y-3">
          <Label htmlFor="image-upload" className="text-base font-medium">Upload Your Image</Label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-purple-400 transition-colors">
            <input
              id="image-upload"
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
            />
            <Label htmlFor="image-upload" className="cursor-pointer">
              <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm text-gray-600">Click to upload or drag and drop</p>
              <p className="text-xs text-gray-400 mt-1">PNG, JPG, WEBP up to 10MB</p>
            </Label>
          </div>
        </div>

        {/* Transformation Prompt */}
        <div className="space-y-3">
          <Label htmlFor="transform-prompt" className="text-base font-medium">Describe Your Transformation</Label>
          <Textarea
            id="transform-prompt"
            placeholder="Convert to Ghibli anime style, make it look like an action hero, apply sepia vintage effect, turn into grayscale, make it more vibrant..."
            value={transformPrompt}
            onChange={(e) => setTransformPrompt(e.target.value)}
            rows={3}
            className="resize-none border-gray-200 focus:border-purple-400 focus:ring-purple-400"
          />
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <Sparkles className="h-3 w-3" />
            <span>Try: "Ghibli anime style", "vintage photograph", "watercolor painting", "cyberpunk style", "comic book art"</span>
          </div>
        </div>

        {/* Convert Button */}
        <Button
          onClick={convertImage}
          disabled={!originalImage || !transformPrompt.trim() || isConverting || !validateAndEnhancePrompt(transformPrompt).isValid}
          className="w-full h-12 text-base font-medium bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isConverting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              AI Processing Image...
            </>
          ) : !originalImage ? (
            <>
              <Upload className="h-4 w-4 mr-2" />
              Upload Image First
            </>
          ) : !transformPrompt.trim() ? (
            <>
              <Palette className="h-4 w-4 mr-2" />
              Describe Transformation
            </>
          ) : !validateAndEnhancePrompt(transformPrompt).isValid ? (
            <>
              <Palette className="h-4 w-4 mr-2" />
              Prompt Too Short
            </>
          ) : (
            <>
              <Sparkles className="h-4 w-4 mr-2" />
              Transform with AI
            </>
          )}
        </Button>

        {/* AI Prompt Analysis */}
        {transformPrompt.trim() && (
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Sparkles className="h-4 w-4 text-purple-600" />
              <span className="font-medium text-purple-800">
                AI Transformation Preview
              </span>
            </div>
            {(() => {
              const promptValidation = validateAndEnhancePrompt(transformPrompt);
              if (promptValidation.isValid) {
                return (
                  <div className="space-y-2">
                    <p className="text-sm text-green-700 font-medium">
                      ✓ AI understands your request: {promptValidation.category} style
                    </p>
                    <p className="text-sm text-gray-600">
                      Enhanced prompt: "{promptValidation.enhancedPrompt.substring(0, 100)}..."
                    </p>
                    <div className="mt-2 pt-2 border-t border-purple-200">
                      <p className="text-xs text-purple-600">
                        🤖 Ready for AI transformation! This will use advanced AI models to transform your image.
                      </p>
                    </div>
                  </div>
                );
              } else {
                return (
                  <div className="space-y-2">
                    <p className="text-sm text-amber-700">
                      ⚠ Please provide a more detailed description.
                    </p>
                    <p className="text-xs text-amber-600">
                      Try: "convert to Ghibli anime style", "make it look like a vintage photograph", "transform into watercolor painting"
                    </p>
                  </div>
                );
              }
            })()}
          </div>
        )}

        {/* AI Transformation Examples */}
        {!originalImage && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <Sparkles className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-gray-800">AI Transformation Examples</span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
              {transformationExamples.map((example, index) => (
                <div key={index} className="bg-white p-3 rounded border">
                  <p className="font-medium text-purple-700 mb-1">"{example.prompt}"</p>
                  <p className="text-gray-600 text-xs">{example.description}</p>
                </div>
              ))}
            </div>
            <p className="text-xs text-gray-500 mt-3">
              💡 Upload an image and try any of these prompts, or create your own transformation description!
            </p>
          </div>
        )}

        {/* Image Preview */}
        <div className="grid md:grid-cols-2 gap-4">
          {originalImage && (
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Original Image</Label>
              <div className="relative rounded-lg overflow-hidden shadow-md">
                <img src={originalImage} alt="Original" className="w-full h-48 object-cover" />
              </div>
            </div>
          )}
          
          {convertedImage && (
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                AI Transformed ({detectedStyle} style)
              </Label>
              <div className="relative rounded-lg overflow-hidden shadow-md">
                <img src={convertedImage} alt="AI Transformed" className="w-full h-48 object-cover" />
              </div>
              <Button
                onClick={downloadImage}
                variant="outline"
                className="w-full border-2 border-purple-200 hover:border-purple-400 hover:bg-purple-50"
              >
                <Download className="h-4 w-4 mr-2" />
                Download AI Transformed Image
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ImageConverter;
