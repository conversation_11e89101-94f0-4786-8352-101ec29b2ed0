import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Upload, Wand2, Download, Sparkles, Zap, Palette, ImageIcon, Camera, ArrowLeft } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useFirebaseImages } from "@/hooks/useFirebaseImages";
import { useNavigate } from "react-router-dom";
import ImageConverter, { styleOptions, applyImageFilter } from "@/components/ImageConverter";
// --- Add missing Select component imports ---
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";

const Generate = () => {
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const [uploadedImage, setUploadedImage] = useState<File | null>(null);
  const [editPrompt, setEditPrompt] = useState("");
  const { user, isLoaded, isSignedIn } = useAuth();
  const { saveGeneratedImage } = useFirebaseImages();
  const navigate = useNavigate();

  const HUGGING_FACE_API_KEY = "*************************************";

  // --- NEW STATE FOR IMAGE EDITING (conversion) ---
  const [editStyle, setEditStyle] = useState<string>("");
  const [convertedEditImage, setConvertedEditImage] = useState<string | null>(null);
  const [isConvertingEdit, setIsConvertingEdit] = useState(false);

  const handleTextToImage = async () => {
    if (!prompt.trim()) {
      toast.error("Please enter a prompt");
      return;
    }

    setIsGenerating(true);
    try {
      toast.info("Generating image with AI...");
      
      const response = await fetch(
        "https://api-inference.huggingface.co/models/stabilityai/stable-diffusion-xl-base-1.0",
        {
          headers: {
            Authorization: `Bearer ${HUGGING_FACE_API_KEY}`,
            "Content-Type": "application/json",
          },
          method: "POST",
          body: JSON.stringify({
            inputs: prompt,
            parameters: {
              negative_prompt: "blurry, low quality, distorted",
              num_inference_steps: 50,
              guidance_scale: 7.5,
              width: 1024,
              height: 1024
            }
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.text();
        
        if (response.status === 503) {
          toast.error("Model is loading, please try again in a few moments");
          return;
        } else if (response.status === 429) {
          toast.error("Rate limit exceeded, please try again later");
          return;
        } else if (response.status === 403) {
          toast.error("API key authentication failed. Please check your Hugging Face API key permissions.");
          return;
        } else if (response.status === 401) {
          toast.error("Invalid API key. Please verify your Hugging Face API key.");
          return;
        } else {
          throw new Error(`API request failed: ${response.status} - ${errorData}`);
        }
      }

      const imageBlob = await response.blob();
      const imageUrl = URL.createObjectURL(imageBlob);
      setGeneratedImage(imageUrl);
      
      // Convert blob to data URL for Firebase saving
      const reader = new FileReader();
      reader.onloadend = async () => {
        const dataUrl = reader.result as string;
        if (user) {
          await saveGeneratedImage(prompt, dataUrl);
        }
      };
      reader.readAsDataURL(imageBlob);
      
      toast.success("High-definition image generated successfully!");
      
    } catch (error) {
      console.error('Error generating image:', error);
      toast.error(`Failed to generate image: ${error.message}`);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleImageEdit = async () => {
    if (!uploadedImage) {
      toast.error("Please upload an image");
      return;
    }
    if (!editPrompt.trim()) {
      toast.error("Please enter an edit prompt");
      return;
    }

    // Convert uploaded image to base64 for processing
    const reader = new FileReader();
    reader.onload = () => {
      const imageDataUrl = reader.result as string;
      
      // For now, we'll apply a simple filter based on the prompt
      // This is a basic implementation - you can enhance it further
      const img = new window.Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);

        // Apply simple effects based on prompt keywords
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        if (editPrompt.toLowerCase().includes('grayscale') || editPrompt.toLowerCase().includes('black and white')) {
          // Convert to grayscale
          for (let i = 0; i < data.length; i += 4) {
            const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
            data[i] = gray;
            data[i + 1] = gray;
            data[i + 2] = gray;
          }
        } else if (editPrompt.toLowerCase().includes('sepia') || editPrompt.toLowerCase().includes('vintage')) {
          // Apply sepia effect
          for (let i = 0; i < data.length; i += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];
            data[i] = Math.min(255, r * 0.393 + g * 0.769 + b * 0.189);
            data[i + 1] = Math.min(255, r * 0.349 + g * 0.686 + b * 0.168);
            data[i + 2] = Math.min(255, r * 0.272 + g * 0.534 + b * 0.131);
          }
        } else if (editPrompt.toLowerCase().includes('bright') || editPrompt.toLowerCase().includes('vibrant')) {
          // Increase brightness
          for (let i = 0; i < data.length; i += 4) {
            data[i] = Math.min(255, data[i] * 1.3);
            data[i + 1] = Math.min(255, data[i + 1] * 1.3);
            data[i + 2] = Math.min(255, data[i + 2] * 1.3);
          }
        }

        ctx.putImageData(imageData, 0, 0);
        const editedImageUrl = canvas.toDataURL('image/png');
        setGeneratedImage(editedImageUrl);
        
        toast.success("Image edited successfully!");
      };
      img.src = imageDataUrl;
    };
    reader.readAsDataURL(uploadedImage);
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      setUploadedImage(file);
      toast.success("Image uploaded successfully!");
    } else {
      toast.error("Please select a valid image file");
    }
  };

  const downloadImage = () => {
    if (!generatedImage) return;
    
    const link = document.createElement('a');
    link.href = generatedImage;
    link.download = `ai-generated-${Date.now()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleImageEditConvert = () => {
    if (!uploadedImage || !editStyle) {
      toast.error("Please upload an image and select a style");
      return;
    }

    const reader = new FileReader();
    setIsConvertingEdit(true);
    reader.onload = (e) => {
      const imgSrc = e.target?.result as string;
      const img = new window.Image();
      img.onload = () => {
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");
        if (!ctx) {
          setIsConvertingEdit(false);
          return;
        }
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);
        applyImageFilter(canvas, ctx, editStyle);

        const convertedURL = canvas.toDataURL("image/png");
        setConvertedEditImage(convertedURL);
        setIsConvertingEdit(false);
        toast.success(`Image converted to ${styleOptions.find(s => s.value === editStyle)?.label} style!`);
      };
      img.onerror = () => {
        toast.error("Failed to load image for editing.");
        setIsConvertingEdit(false);
      };
      img.src = imgSrc;
    };
    reader.readAsDataURL(uploadedImage);
  };

  const downloadEditImage = () => {
    if (!convertedEditImage) return;
    const link = document.createElement('a');
    link.href = convertedEditImage;
    link.download = `converted-${editStyle}-${Date.now()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading AI Studio...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      {/* Top Navigation Bar */}
      <div className="sticky top-0 z-40 bg-white/80 backdrop-blur-lg border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-4">
              <Button variant="ghost" onClick={() => navigate('/')} className="flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back to Home
              </Button>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                AI Creative Studio
              </h1>
            </div>
            
            <div className="flex items-center gap-2 bg-gradient-to-r from-purple-100 to-blue-100 px-4 py-2 rounded-full">
              <Sparkles className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium text-gray-700">Powered by AI</span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {!isSignedIn ? (
          <Card className="max-w-md mx-auto shadow-xl border-0 bg-white/90 backdrop-blur-sm">
            <CardContent className="pt-8 text-center">
              <Wand2 className="h-12 w-12 mx-auto mb-4 text-purple-500" />
              <h3 className="text-xl font-semibold mb-2">Welcome to AI Studio</h3>
              <p className="text-gray-600 mb-6">Please sign in to start creating amazing images with AI</p>
              <Button className="w-full" onClick={() => navigate('/')}>
                Go to Sign In
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid lg:grid-cols-4 gap-8">
            {/* Main Creation Area */}
            <div className="lg:col-span-3 space-y-6">
              <Tabs defaultValue="text-to-image" className="w-full">
                <TabsList className="grid w-full grid-cols-3 bg-white/90 backdrop-blur-sm border border-gray-200/50">
                  <TabsTrigger value="text-to-image" className="flex items-center gap-2">
                    <Wand2 className="h-4 w-4" />
                    Text to Image
                  </TabsTrigger>
                  <TabsTrigger value="image-edit" className="flex items-center gap-2">
                    <Palette className="h-4 w-4" />
                    Image Editing
                  </TabsTrigger>
                  <TabsTrigger value="style-convert" className="flex items-center gap-2">
                    <Camera className="h-4 w-4" />
                    Style Converter
                  </TabsTrigger>
                </TabsList>
                
                <TabsContent value="text-to-image">
                  <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                    <CardHeader className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-t-lg">
                      <CardTitle className="flex items-center gap-2">
                        <Zap className="h-5 w-5 text-purple-600" />
                        AI Image Generation
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-6 space-y-6">
                      <div className="space-y-3">
                        <Label htmlFor="prompt" className="text-base font-medium">Describe Your Vision</Label>
                        <Textarea
                          id="prompt"
                          placeholder="A serene Japanese garden with cherry blossoms, koi pond, and traditional lanterns, photorealistic, golden hour lighting..."
                          value={prompt}
                          onChange={(e) => setPrompt(e.target.value)}
                          rows={4}
                          className="resize-none border-gray-200 focus:border-purple-400 focus:ring-purple-400"
                        />
                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <Sparkles className="h-3 w-3" />
                          <span>Pro tip: Include style, lighting, and quality details for best results</span>
                        </div>
                      </div>
                      
                      <Button
                        onClick={handleTextToImage}
                        disabled={isGenerating}
                        className="w-full h-12 text-base font-medium bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 transition-all duration-200"
                      >
                        {isGenerating ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Creating Your Masterpiece...
                          </>
                        ) : (
                          <>
                            <Wand2 className="h-4 w-4 mr-2" />
                            Generate HD Image
                          </>
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="image-edit">
                  <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                    <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-t-lg">
                      <CardTitle className="flex items-center gap-2">
                        <Upload className="h-5 w-5 text-blue-600" />
                        Image Editing
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-6 space-y-6">
                      {/* Upload Section */}
                      <div className="space-y-3">
                        <Label htmlFor="edit-image-upload" className="text-base font-medium">Upload Your Image</Label>
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-purple-400 transition-colors">
                          <input
                            id="edit-image-upload"
                            type="file"
                            accept="image/*"
                            onChange={handleImageUpload}
                            className="hidden"
                          />
                          <Label htmlFor="edit-image-upload" className="cursor-pointer">
                            <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                            <p className="text-sm text-gray-600">Click to upload or drag and drop</p>
                            <p className="text-xs text-gray-400 mt-1">PNG, JPG, WEBP up to 10MB</p>
                          </Label>
                        </div>
                        {uploadedImage && (
                          <div className="flex items-center gap-2 text-sm text-green-600 bg-green-50 p-2 rounded">
                            <Sparkles className="h-4 w-4" />
                            {uploadedImage.name} uploaded successfully
                          </div>
                        )}
                      </div>

                      {/* Edit Prompt Section */}
                      <div className="space-y-3">
                        <Label htmlFor="edit-prompt" className="text-base font-medium">Describe Your Edit</Label>
                        <Textarea
                          id="edit-prompt"
                          placeholder="Make it grayscale, add sepia effect, make it brighter, apply vintage filter..."
                          value={editPrompt}
                          onChange={(e) => setEditPrompt(e.target.value)}
                          rows={3}
                          className="resize-none border-gray-200 focus:border-purple-400 focus:ring-purple-400"
                        />
                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <Sparkles className="h-3 w-3" />
                          <span>Try keywords like: grayscale, sepia, vintage, bright, vibrant</span>
                        </div>
                      </div>
                      
                      <Button
                        onClick={handleImageEdit}
                        disabled={!uploadedImage || !editPrompt.trim()}
                        className="w-full h-12 text-base font-medium bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
                      >
                        <Palette className="h-4 w-4 mr-2" />
                        Apply Edit
                      </Button>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="style-convert">
                  <ImageConverter />
                </TabsContent>
              </Tabs>

              {/* Generated Image Display */}
              <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ImageIcon className="h-5 w-5 text-purple-600" />
                    Your Creation
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  {generatedImage ? (
                    <div className="space-y-4">
                      <div className="relative rounded-xl overflow-hidden shadow-lg group">
                        <img
                          src={generatedImage}
                          alt="AI Generated"
                          className="w-full h-auto transition-transform duration-300 group-hover:scale-[1.02]"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                      </div>
                      <Button
                        onClick={downloadImage}
                        variant="outline"
                        className="w-full h-12 border-2 border-purple-200 hover:border-purple-400 hover:bg-purple-50"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Download HD Image (1024×1024)
                      </Button>
                    </div>
                  ) : (
                    <div className="h-96 border-2 border-dashed border-gray-200 rounded-xl flex items-center justify-center bg-gradient-to-br from-gray-50 to-white">
                      <div className="text-center text-gray-400">
                        <div className="relative mb-4">
                          <ImageIcon className="h-16 w-16 mx-auto opacity-40" />
                          <Sparkles className="h-6 w-6 absolute -top-1 -right-1 text-purple-400 animate-pulse" />
                        </div>
                        <p className="text-lg font-medium mb-2">Your AI masterpiece will appear here</p>
                        <p className="text-sm">High-definition • Professional quality • Instant generation</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Right Sidebar */}
            <div className="space-y-6">
              <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader className="bg-gradient-to-br from-purple-50 to-blue-50 rounded-t-lg">
                  <CardTitle className="flex items-center gap-2 text-base">
                    <Zap className="h-4 w-4" />
                    Quick Tips
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="space-y-3 text-sm">
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                      <p>Be specific about style, colors, and mood</p>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                      <p>Include lighting details like "golden hour" or "dramatic shadows"</p>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                      <p>Add quality terms like "photorealistic", "highly detailed"</p>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                      <p>Try different art styles: "digital art", "oil painting", "anime"</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-xl border-0 bg-gradient-to-br from-purple-500 to-blue-500 text-white">
                <CardContent className="p-6 text-center">
                  <Sparkles className="h-8 w-8 mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">AI-Powered Creation</h3>
                  <p className="text-white/90 text-sm">
                    Using advanced Stable Diffusion XL for professional-quality results
                  </p>
                </CardContent>
              </Card>

              <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-base flex items-center gap-2">
                    <Camera className="h-4 w-4" />
                    Style Conversion
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="space-y-2 text-sm text-gray-600">
                    <p>Transform your images with artistic filters:</p>
                    <ul className="space-y-1 text-xs">
                      <li>• Grayscale & Sepia tones</li>
                      <li>• Sketch & artistic effects</li>
                      <li>• Vintage & vibrant filters</li>
                      <li>• Custom style transformations</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Generate;
