import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Wand2, Download, Sparkles, Zap, ImageIcon, ArrowLeft } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useFirebaseImages } from "@/hooks/useFirebaseImages";
import { useNavigate } from "react-router-dom";
import ImageConverter from "@/components/ImageConverter";

const Generate = () => {
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const { user, isLoaded, isSignedIn } = useAuth();
  const { saveGeneratedImage } = useFirebaseImages();
  const navigate = useNavigate();

  const HUGGING_FACE_API_KEY = "*************************************";

  const handleTextToImage = async () => {
    if (!prompt.trim()) {
      toast.error("Please enter a prompt");
      return;
    }

    setIsGenerating(true);
    try {
      toast.info("Generating image with AI...");
      
      const response = await fetch(
        "https://api-inference.huggingface.co/models/stabilityai/stable-diffusion-xl-base-1.0",
        {
          headers: {
            Authorization: `Bearer ${HUGGING_FACE_API_KEY}`,
            "Content-Type": "application/json",
          },
          method: "POST",
          body: JSON.stringify({
            inputs: prompt,
            parameters: {
              negative_prompt: "blurry, low quality, distorted",
              num_inference_steps: 50,
              guidance_scale: 7.5,
              width: 1024,
              height: 1024
            }
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.text();
        
        if (response.status === 503) {
          toast.error("Model is loading, please try again in a few moments");
          return;
        } else if (response.status === 429) {
          toast.error("Rate limit exceeded, please try again later");
          return;
        } else if (response.status === 403) {
          toast.error("API key authentication failed. Please check your Hugging Face API key permissions.");
          return;
        } else if (response.status === 401) {
          toast.error("Invalid API key. Please verify your Hugging Face API key.");
          return;
        } else {
          throw new Error(`API request failed: ${response.status} - ${errorData}`);
        }
      }

      const imageBlob = await response.blob();
      const imageUrl = URL.createObjectURL(imageBlob);
      setGeneratedImage(imageUrl);
      
      // Convert blob to data URL for Firebase saving
      const reader = new FileReader();
      reader.onloadend = async () => {
        const dataUrl = reader.result as string;
        if (user) {
          await saveGeneratedImage(prompt, dataUrl);
        }
      };
      reader.readAsDataURL(imageBlob);
      
      toast.success("High-definition image generated successfully!");
      
    } catch (error) {
      console.error('Error generating image:', error);
      toast.error(`Failed to generate image: ${error.message}`);
    } finally {
      setIsGenerating(false);
    }
  };



  const downloadImage = () => {
    if (!generatedImage) return;

    const link = document.createElement('a');
    link.href = generatedImage;
    link.download = `ai-generated-${Date.now()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading AI Studio...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      {/* Top Navigation Bar */}
      <div className="sticky top-0 z-40 bg-white/80 backdrop-blur-lg border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-4">
              <Button variant="ghost" onClick={() => navigate('/')} className="flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back to Home
              </Button>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                AI Creative Studio
              </h1>
            </div>
            
            <div className="flex items-center gap-2 bg-gradient-to-r from-purple-100 to-blue-100 px-4 py-2 rounded-full">
              <Sparkles className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium text-gray-700">Powered by AI</span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {!isSignedIn ? (
          <Card className="max-w-md mx-auto shadow-xl border-0 bg-white/90 backdrop-blur-sm">
            <CardContent className="pt-8 text-center">
              <Wand2 className="h-12 w-12 mx-auto mb-4 text-purple-500" />
              <h3 className="text-xl font-semibold mb-2">Welcome to AI Studio</h3>
              <p className="text-gray-600 mb-6">Please sign in to start creating amazing images with AI</p>
              <Button className="w-full" onClick={() => navigate('/')}>
                Go to Sign In
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid lg:grid-cols-4 gap-8">
            {/* Main Creation Area */}
            <div className="lg:col-span-3 space-y-8">
              {/* AI Image Generation Section */}
              <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-t-lg">
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-purple-600" />
                    AI Image Generation
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6 space-y-6">
                  <div className="space-y-3">
                    <Label htmlFor="prompt" className="text-base font-medium">Describe Your Vision</Label>
                    <Textarea
                      id="prompt"
                      placeholder="A serene Japanese garden with cherry blossoms, koi pond, and traditional lanterns, photorealistic, golden hour lighting..."
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      rows={4}
                      className="resize-none border-gray-200 focus:border-purple-400 focus:ring-purple-400"
                    />
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <Sparkles className="h-3 w-3" />
                      <span>Pro tip: Include style, lighting, and quality details for best results</span>
                    </div>
                  </div>

                  <Button
                    onClick={handleTextToImage}
                    disabled={isGenerating}
                    className="w-full h-12 text-base font-medium bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 transition-all duration-200"
                  >
                    {isGenerating ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Creating Your Masterpiece...
                      </>
                    ) : (
                      <>
                        <Wand2 className="h-4 w-4 mr-2" />
                        Generate HD Image
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>

              {/* Image Editing Section */}
              <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-t-lg">
                  <CardTitle className="flex items-center gap-2">
                    <ImageIcon className="h-5 w-5 text-blue-600" />
                    Image Style Transformation
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <ImageConverter />
                </CardContent>
              </Card>


              {/* Generated Image Display */}
              {generatedImage && (
                <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <ImageIcon className="h-5 w-5 text-purple-600" />
                      Your AI Creation
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div className="relative rounded-xl overflow-hidden shadow-lg group">
                        <img
                          src={generatedImage}
                          alt="AI Generated"
                          className="w-full h-auto transition-transform duration-300 group-hover:scale-[1.02]"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                      </div>
                      <Button
                        onClick={downloadImage}
                        variant="outline"
                        className="w-full h-12 border-2 border-purple-200 hover:border-purple-400 hover:bg-purple-50"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Download HD Image (1024×1024)
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Right Sidebar */}
            <div className="space-y-6">
              <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader className="bg-gradient-to-br from-purple-50 to-blue-50 rounded-t-lg">
                  <CardTitle className="flex items-center gap-2 text-base">
                    <Zap className="h-4 w-4" />
                    AI Generation Tips
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="space-y-3 text-sm">
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                      <p>Be specific about style, colors, and mood</p>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                      <p>Include lighting details like "golden hour" or "dramatic shadows"</p>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                      <p>Add quality terms like "photorealistic", "highly detailed"</p>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                      <p>Try different art styles: "digital art", "oil painting", "anime"</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-xl border-0 bg-gradient-to-br from-purple-500 to-blue-500 text-white">
                <CardContent className="p-6 text-center">
                  <Sparkles className="h-8 w-8 mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">AI-Powered Studio</h3>
                  <p className="text-white/90 text-sm">
                    Generate stunning images and transform existing photos with advanced AI
                  </p>
                </CardContent>
              </Card>

              <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-base flex items-center gap-2">
                    <ImageIcon className="h-4 w-4" />
                    Style Transformations
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="space-y-2 text-sm text-gray-600">
                    <p>Available artistic styles:</p>
                    <ul className="space-y-1 text-xs">
                      <li>• Ghibli anime style</li>
                      <li>• Action hero cinematic</li>
                      <li>• Grayscale & Sepia tones</li>
                      <li>• Sketch & artistic effects</li>
                      <li>• Vintage & vibrant filters</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Generate;
