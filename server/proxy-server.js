const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Enable CORS for all routes
app.use(cors({
  origin: ['http://localhost:8082', 'http://localhost:3000', 'http://localhost:5173'],
  credentials: true
}));

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Replicate API Proxy Server is running' });
});

// Proxy endpoint for Replicate API
app.post('/api/transform-image', async (req, res) => {
  try {
    const { imageDataUrl, prompt } = req.body;
    
    if (!imageDataUrl || !prompt) {
      return res.status(400).json({ 
        error: 'Missing required fields: imageDataUrl and prompt' 
      });
    }

    const REPLICATE_API_TOKEN = process.env.VITE_REPLICATE_API_TOKEN;
    
    if (!REPLICATE_API_TOKEN) {
      return res.status(500).json({ 
        error: 'Replicate API token not configured on server' 
      });
    }

    console.log('🤖 Processing AI transformation request:', prompt);

    // Create prediction with Replicate API
    const predictionResponse = await fetch('https://api.replicate.com/v1/models/black-forest-labs/flux-kontext-pro/predictions', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${REPLICATE_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        input: {
          input_image: imageDataUrl,
          prompt: prompt,
          aspect_ratio: 'match_input_image',
          output_format: 'png',
          safety_tolerance: 2,
          seed: Math.floor(Math.random() * 1000000),
        }
      })
    });

    if (!predictionResponse.ok) {
      const errorData = await predictionResponse.json();
      console.error('Replicate API error:', errorData);

      // Handle specific error cases
      if (predictionResponse.status === 402 && errorData.title === 'Billing required') {
        return res.status(402).json({
          error: `💳 Billing Required: Your Replicate account needs billing setup to use AI models.

🔧 **Quick Fix:**
1. Visit: https://replicate.com/account/billing
2. Add a payment method (free credits available)
3. Wait a few minutes for activation
4. Try the transformation again

💡 **Note**: Most AI models on Replicate require billing setup, even for free tier usage.`,
          type: 'billing_required',
          billingUrl: 'https://replicate.com/account/billing'
        });
      }

      return res.status(predictionResponse.status).json({
        error: `Replicate API error: ${errorData.detail || predictionResponse.statusText}`,
        type: 'api_error'
      });
    }

    const prediction = await predictionResponse.json();
    console.log('Prediction created:', prediction.id);

    // Poll for completion
    let result = prediction;
    let attempts = 0;
    const maxAttempts = 60; // 60 seconds timeout

    while ((result.status === 'starting' || result.status === 'processing') && attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      attempts++;

      const statusResponse = await fetch(`https://api.replicate.com/v1/predictions/${result.id}`, {
        headers: {
          'Authorization': `Token ${REPLICATE_API_TOKEN}`,
        }
      });

      if (!statusResponse.ok) {
        console.error('Failed to check prediction status:', statusResponse.statusText);
        return res.status(500).json({ 
          error: `Failed to check prediction status: ${statusResponse.statusText}` 
        });
      }

      result = await statusResponse.json();
      console.log(`Prediction status (${attempts}s):`, result.status);
    }

    if (result.status === 'failed') {
      console.error('AI transformation failed:', result.error);
      return res.status(500).json({ 
        error: `AI transformation failed: ${result.error || 'Unknown error'}` 
      });
    }

    if (result.status === 'succeeded' && result.output) {
      console.log('✅ AI transformation completed successfully');
      return res.json({ 
        success: true, 
        outputUrl: result.output,
        predictionId: result.id 
      });
    }

    // Timeout or unexpected status
    return res.status(408).json({ 
      error: 'AI transformation timed out or returned unexpected status',
      status: result.status,
      predictionId: result.id 
    });

  } catch (error) {
    console.error('Server error:', error);
    res.status(500).json({ 
      error: 'Internal server error: ' + error.message 
    });
  }
});

app.listen(PORT, () => {
  console.log(`🚀 Replicate API Proxy Server running on http://localhost:${PORT}`);
  console.log(`📋 Health check: http://localhost:${PORT}/health`);
  console.log(`🎨 Transform endpoint: POST http://localhost:${PORT}/api/transform-image`);
});
