const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Enable CORS for all routes
app.use(cors({
  origin: ['http://localhost:8082', 'http://localhost:3000', 'http://localhost:5173'],
  credentials: true
}));

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'DeepAI API Proxy Server is running' });
});

// Proxy endpoint for DeepAI API
app.post('/api/transform-image', async (req, res) => {
  try {
    const { imageDataUrl, prompt } = req.body;

    if (!imageDataUrl || !prompt) {
      return res.status(400).json({
        error: 'Missing required fields: imageDataUrl and prompt'
      });
    }

    const DEEPAI_API_KEY = process.env.VITE_DEEPAI_API_KEY;

    if (!DEEPAI_API_KEY) {
      return res.status(500).json({
        error: 'DeepAI API key not configured on server'
      });
    }

    console.log('🤖 Processing AI transformation request with DeepAI:', prompt);

    // Convert data URL to blob for DeepAI API
    const base64Data = imageDataUrl.split(',')[1];
    const imageBuffer = Buffer.from(base64Data, 'base64');

    // Create FormData for DeepAI API
    const FormData = require('form-data');
    const formData = new FormData();
    formData.append('image', imageBuffer, { filename: 'image.png', contentType: 'image/png' });
    formData.append('text', prompt);

    // Call DeepAI Image Editor API
    const deepaiResponse = await fetch('https://api.deepai.org/api/image-editor', {
      method: 'POST',
      headers: {
        'api-key': DEEPAI_API_KEY,
        ...formData.getHeaders()
      },
      body: formData
    });

    if (!deepaiResponse.ok) {
      const errorText = await deepaiResponse.text();
      console.error('❌ DeepAI API error:', errorText);

      // Check for various error types
      if (deepaiResponse.status === 402) {
        return res.status(402).json({
          error: 'Payment required',
          message: 'DeepAI API requires payment for this request. Please check your account at https://deepai.org/pricing',
          details: errorText
        });
      }

      if (deepaiResponse.status === 401) {
        return res.status(401).json({
          error: 'Invalid API key',
          message: 'DeepAI API key is invalid or missing. Please check your configuration.',
          details: errorText
        });
      }

      return res.status(deepaiResponse.status).json({
        error: 'DeepAI API error',
        details: errorText
      });
    }

    const result = await deepaiResponse.json();
    console.log('✅ DeepAI transformation completed');

    if (result.output_url) {
      return res.json({
        success: true,
        imageUrl: result.output_url
      });
    } else {
      console.error('❌ Unexpected result from DeepAI:', result);
      return res.status(500).json({
        error: 'Unexpected result from DeepAI service',
        details: result
      });
    }

  } catch (error) {
    console.error('Server error:', error);
    res.status(500).json({ 
      error: 'Internal server error: ' + error.message 
    });
  }
});

app.listen(PORT, () => {
  console.log(`🚀 DeepAI API Proxy Server running on http://localhost:${PORT}`);
  console.log(`📋 Health check: http://localhost:${PORT}/health`);
  console.log(`🎨 Transform endpoint: POST http://localhost:${PORT}/api/transform-image`);
});
