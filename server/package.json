{"name": "imagemagic-proxy-server", "version": "1.0.0", "description": "Proxy server for Replicate API to handle CORS issues", "main": "proxy-server.js", "scripts": {"start": "node proxy-server.js", "dev": "nodemon proxy-server.js"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "form-data": "^4.0.3", "node-fetch": "^2.6.7"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["replicate", "api", "proxy", "cors", "ai", "image-transformation"], "author": "ImageMagic", "license": "MIT"}