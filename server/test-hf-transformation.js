import fetch from 'node-fetch';
import fs from 'fs';

// Test the Hugging Face Spaces transformation
async function testTransformation() {
  try {
    console.log('🧪 Testing Hugging Face Spaces AI transformation...');
    
    // Create a simple test image data URL (1x1 red pixel)
    const testImageDataUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
    
    const response = await fetch('http://localhost:3001/api/transform-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        imageDataUrl: testImageDataUrl,
        prompt: 'Convert this image to Studio Ghibli anime style'
      })
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Transformation successful!');
      console.log('Result:', result);
    } else {
      console.log('❌ Transformation failed:');
      console.log('Status:', response.status);
      console.log('Error:', result);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testTransformation();
