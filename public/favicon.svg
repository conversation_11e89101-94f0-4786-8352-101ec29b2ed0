<svg width="32" height="32" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="wandGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#A855F7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="frameGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
    
    <radialGradient id="sparkleGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FBBF24;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F59E0B;stop-opacity:0.8" />
    </radialGradient>
    
    <radialGradient id="bgGradient" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#F8FAFC;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E2E8F0;stop-opacity:1" />
    </radialGradient>
  </defs>
  
  <circle cx="32" cy="32" r="30" fill="url(#bgGradient)" stroke="#CBD5E1" stroke-width="2"/>
  <rect x="12" y="18" width="24" height="18" rx="2" ry="2" 
        fill="none" stroke="url(#frameGradient)" stroke-width="3"/>
  <circle cx="18" cy="24" r="2.5" fill="url(#frameGradient)" opacity="0.7"/>
  <rect x="23" y="21" width="10" height="8" rx="1" fill="url(#frameGradient)" opacity="0.5"/>
  <line x1="32" y1="36" x2="45" y2="23" stroke="url(#wandGradient)" stroke-width="4" stroke-linecap="round"/>
  <g transform="translate(45, 23)">
    <path d="M0,-4 L1.5,0 L4,0 L1.5,1.5 L0,5 L-1.5,1.5 L-4,0 L-1.5,0 Z" 
          fill="url(#sparkleGradient)" stroke="#FBBF24" stroke-width="1"/>
  </g>
  <circle cx="40" cy="18" r="2" fill="url(#sparkleGradient)" opacity="0.9"/>
  <circle cx="20" cy="12" r="1.5" fill="url(#sparkleGradient)" opacity="0.7"/>
  <circle cx="48" cy="30" r="1.8" fill="url(#sparkleGradient)" opacity="0.8"/>
  <circle cx="15" cy="42" r="1.2" fill="url(#sparkleGradient)" opacity="0.6"/>
  <text x="32" y="52" font-family="Inter, system-ui, sans-serif" font-size="12" font-weight="800" 
        fill="url(#wandGradient)" text-anchor="middle">IM</text>
</svg>