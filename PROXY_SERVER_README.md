# 🚀 AI Image Transformation - Proxy Server Setup

## Overview

This setup enables **real AI-powered image transformations** using DeepAI's Image Editor API by solving CORS (Cross-Origin Resource Sharing) restrictions that prevent direct browser-to-API calls.

## 🔧 Quick Setup

### 1. **Start the Proxy Server**
```bash
# Navigate to server directory
cd server

# Install dependencies (first time only)
npm install

# Start the proxy server
npm start
```

The server will start on `http://localhost:3001`

### 2. **Use the Application**
- Keep the proxy server running in one terminal
- The React app (running on `http://localhost:8082`) will automatically detect and use the proxy server
- Upload an image and try AI transformations like "convert to Ghibli anime style"

## 🎨 Supported AI Transformations

- **Studio Ghibli anime style** - Soft, dreamy anime-inspired look
- **Action hero cinematic style** - Bold, dramatic superhero aesthetic  
- **Vintage photograph style** - Classic retro film look with warm tones
- **Watercolor painting style** - Artistic watercolor effect with soft edges
- **Oil painting style** - Rich, textured oil painting aesthetic
- **Cyberpunk style** - Futuristic neon-lit cyberpunk aesthetic
- **Comic book style** - Bold comic book illustration style
- **Black and white** - Classic monochrome photography

## 🔍 How It Works

1. **Frontend** uploads image and sends transformation request
2. **Proxy Server** receives request and forwards to DeepAI API
3. **DeepAI API** processes image using AI Image Editor model
4. **Proxy Server** returns the transformed image URL
5. **Frontend** displays the transformed image

## 🛠 Technical Details

### Proxy Server Features:
- **CORS Handling**: Enables browser-to-API communication
- **Request Forwarding**: Securely forwards requests to DeepAI API
- **Direct Response**: Handles immediate AI processing results
- **Error Handling**: Provides detailed error messages
- **Security**: API key stays on server-side

### API Endpoints:
- `GET /health` - Server health check
- `POST /api/transform-image` - Image transformation endpoint

### Environment Variables:
- `VITE_DEEPAI_API_KEY` - Your DeepAI API key (needs configuration)

## 🚨 Troubleshooting

### If transformations fail:
1. **Check proxy server is running**: Visit `http://localhost:3001/health`
2. **Check API token**: Ensure `.env` file exists in server directory
3. **Check console logs**: Look for error messages in browser developer tools
4. **Restart servers**: Stop and restart both proxy server and React app

### Common Issues:
- **"Failed to fetch"**: Proxy server not running
- **"CORS Error"**: Proxy server not accessible
- **"API key not configured"**: Missing or invalid DeepAI API key

## 🔄 Development Workflow

1. **Start proxy server**: `cd server && npm start`
2. **Start React app**: `npm run dev` (in main directory)
3. **Test transformations**: Upload image and try different styles
4. **Check logs**: Monitor both server and browser console for debugging

## 📚 Production Deployment

For production, consider:
- **Serverless Functions**: Deploy proxy as Vercel/Netlify function
- **Backend Integration**: Integrate into existing Node.js/Express server
- **Environment Security**: Use secure environment variable management
- **Rate Limiting**: Implement API rate limiting and usage monitoring

The current setup is perfect for development and testing real AI transformations!
