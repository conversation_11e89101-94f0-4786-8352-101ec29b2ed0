# Hugging Face Spaces Configuration
# No API key required - using free Hugging Face Spaces
# VITE_HF_TOKEN=your_hugging_face_token_here (optional)

# This application uses Hugging Face Spaces for completely free AI image transformations
# No API key or billing setup required!
# Using the Hexii/Neural-Style-Transfer space for style transfer operations
# Optional: You can add a Hugging Face token for potentially faster processing

# Firebase Configuration (if using Firebase for other features)
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id

# Clerk Authentication (if using Clerk for auth)
VITE_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
